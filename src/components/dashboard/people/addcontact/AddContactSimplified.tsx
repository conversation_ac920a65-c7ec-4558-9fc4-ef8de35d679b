"use client"

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { ArrowLeft, CheckCircle, Loader2, Search, ExternalLink, AlertTriangle } from "lucide-react"
import { debounce } from 'lodash'
import Link from 'next/link'
import { ContactFormData, CompanySuggestion, ExistingContact } from '../shared/types'
import { DuplicateContactDialog } from '../components/DuplicateContactDialog'
import ContactFormFields from './ContactFormFields'
import CompanySearchField from './CompanySearchField'
import InvestmentAdditionSection from './InvestmentAdditionSection'
import { ContactValidationService, ValidationStates } from './ValidationUtils'
import { ContactSubmissionService } from './ContactSubmissionService'

interface AddContactSimplifiedProps {
  onBack: () => void;
  companyId?: string;
  preSelectedCompany?: CompanySuggestion;
  onSuccess?: (contactId: number) => void;
}

const AddContactSimplified: React.FC<AddContactSimplifiedProps> = ({ 
  onBack, 
  companyId, 
  preSelectedCompany,
  onSuccess
}) => {
  const [formData, setFormData] = useState<ContactFormData>(
    ContactSubmissionService.getInitialFormData(preSelectedCompany)
  );

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [duplicateContacts, setDuplicateContacts] = useState<ExistingContact[]>([]);
  const [showDuplicateDialog, setShowDuplicateDialog] = useState(false);
  const [investmentCriteria, setInvestmentCriteria] = useState<any[]>([]);
  const [selectedCompanyIC, setSelectedCompanyIC] = useState<any[]>([]);
  
  // Company search state
  const [isSearching, setIsSearching] = useState(false);
  const [companySuggestions, setCompanySuggestions] = useState<CompanySuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<CompanySuggestion | null>(preSelectedCompany || null);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  
  // Email and name search state
  const [emailSuggestions, setEmailSuggestions] = useState<any[]>([]);
  const [showEmailSuggestions, setShowEmailSuggestions] = useState(false);
  const [selectedEmailSuggestionIndex, setSelectedEmailSuggestionIndex] = useState(-1);
  const [isSearchingEmail, setIsSearchingEmail] = useState(false);
  const [activeEmailField, setActiveEmailField] = useState<'email' | 'additional_email' | 'name_search' | null>(null);
  const emailSuggestionsRef = useRef<HTMLDivElement>(null);
  
  // Validation state
  const [validationStates, setValidationStates] = useState<ValidationStates>({
    email: { isValidating: false, isDuplicate: false },
    additional_email: { isValidating: false, isDuplicate: false },
    linkedin_url: { isValidating: false, isDuplicate: false },
    full_name: { isValidating: false, isDuplicate: false }
  });

  // Filter options
  const [filterOptions, setFilterOptions] = useState<{
    jobTiers: string[];
  }>({
    jobTiers: []
  });

  // Fetch filter options on component mount
  useEffect(() => {
    const fetchFilterOptions = async () => {
      const options = await ContactSubmissionService.fetchFilterOptions();
      setFilterOptions(options);
    };

    fetchFilterOptions();
  }, []);

  // Fetch company data if companyId is provided
  useEffect(() => {
    const fetchCompanyData = async () => {
      if (companyId && !preSelectedCompany) {
        const transformedCompany = await ContactSubmissionService.fetchCompanyData(companyId);
        if (transformedCompany) {
          setSelectedCompany(transformedCompany);
          setFormData(prev => ({
            ...prev,
            company_name: transformedCompany.company_name,
            company_website: transformedCompany.company_website || '',
            industry: transformedCompany.industry || '',
            company_address: transformedCompany.company_address || '',
            company_city: transformedCompany.company_city || '',
            company_state: transformedCompany.company_state || '',
            company_country: transformedCompany.company_country || '',
            company_zip: transformedCompany.company_zip || ''
          }));
        }
      }
    };

    fetchCompanyData();
  }, [companyId, preSelectedCompany]);

  // Click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle email suggestions
      if (emailSuggestionsRef.current && !emailSuggestionsRef.current.contains(event.target as Node)) {
        const emailSearchResults = document.querySelector('[data-email-search-results]');
        if (emailSearchResults && emailSearchResults.contains(event.target as Node)) {
          return;
        }
        setShowEmailSuggestions(false);
        setSelectedEmailSuggestionIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Debounced name search function
  const debouncedSearchByName = useCallback(
    debounce(async (firstName: string, lastName: string) => {
      console.log('Name search called with:', firstName, lastName);
      
      if (!firstName || !lastName || (firstName.length + lastName.length < 4)) {
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
        setIsSearchingEmail(false);
        return;
      }

      setIsSearchingEmail(true);
      setActiveEmailField('name_search');
      
      try {
        const fullName = `${firstName} ${lastName}`;
        const response = await fetch(`/api/contacts?search=${encodeURIComponent(fullName)}&limit=5`);
        
        if (response.ok) {
          const data = await response.json();
          console.log('Name search results:', data);
          setEmailSuggestions(data.contacts || []);
          setShowEmailSuggestions(data.contacts && data.contacts.length > 0);
        } else {
          setEmailSuggestions([]);
          setShowEmailSuggestions(false);
        }
      } catch (error) {
        console.error('Error during name search:', error);
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
      } finally {
        setIsSearchingEmail(false);
      }
    }, 500),
    []
  );

  // Debounced email search function
  const debouncedSearchEmails = useCallback(
    debounce(async (email: string, fieldName: 'email' | 'additional_email') => {
      console.log('Email search called with:', email, 'Field:', fieldName);
      
      if (email.length < 3) {
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
        setIsSearchingEmail(false);
        return;
      }

      setIsSearchingEmail(true);
      setActiveEmailField(fieldName);
      
      try {
        const response = await fetch(`/api/contacts/search-by-email?email=${encodeURIComponent(email)}`);
        
        if (response.ok) {
          const data = await response.json();
          console.log('Email search results:', data);
          setEmailSuggestions(data.contacts || []);
          setShowEmailSuggestions(data.contacts && data.contacts.length > 0);
        } else {
          setEmailSuggestions([]);
          setShowEmailSuggestions(false);
        }
      } catch (error) {
        console.error('Error during email search:', error);
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
      } finally {
        setIsSearchingEmail(false);
      }
    }, 500),
    []
  );

  // Handle email contact selection
  const handleEmailContactSelect = (contact: any) => {
    // Show duplicate warning and ask user what they want to do
    const message = `A contact with email "${contact.email}" already exists:\n\n` +
      `Name: ${contact.first_name} ${contact.last_name}\n` +
      `Company: ${contact.company_name || 'N/A'}\n` +
      `Title: ${contact.title || 'N/A'}\n\n` +
      `Would you like to:\n` +
      `- Edit the existing contact instead\n` +
      `- Continue editing current contact`;

    if (confirm(message)) {
      // User wants to edit existing contact - redirect to contact detail page
      window.open(`/dashboard/people/${contact.contact_id}`, '_blank');
    }
    
    // Clear email search suggestions
    setShowEmailSuggestions(false);
    setEmailSuggestions([]);
    setSelectedEmailSuggestionIndex(-1);
  };

  // Keyboard navigation for email suggestions
  const handleEmailKeyDown = (e: React.KeyboardEvent) => {
    if (!showEmailSuggestions || emailSuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedEmailSuggestionIndex((prev) => 
          prev < emailSuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedEmailSuggestionIndex((prev) => 
          prev > 0 ? prev - 1 : emailSuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedEmailSuggestionIndex >= 0) {
          handleEmailContactSelect(emailSuggestions[selectedEmailSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowEmailSuggestions(false);
        setSelectedEmailSuggestionIndex(-1);
        break;
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Trigger company search when company_name changes
    if (name === 'company_name') {
      if (selectedCompany && value !== selectedCompany.company_name) {
        setSelectedCompany(null);
      }
      
      setSelectedSuggestionIndex(-1);
      if (value.trim() && value.trim().length >= 2) {
        debouncedSearchCompanies(value);
      } else if (value.trim().length < 2) {
        setShowSuggestions(false);
        setCompanySuggestions([]);
      }
    }

    // Trigger email search when email fields change
    if (name === 'email' || name === 'additional_email') {
      debouncedSearchEmails(value, name as 'email' | 'additional_email');
      debouncedValidateEmail(value, name as 'email' | 'additional_email');
    } else if (name === 'linkedin_url') {
      debouncedValidateLinkedIn(value);
    } else if (name === 'first_name' || name === 'last_name') {
      const firstName = name === 'first_name' ? value : formData.first_name;
      const lastName = name === 'last_name' ? value : formData.last_name;
      
      if (firstName && lastName) {
        debouncedSearchByName(firstName, lastName);
        debouncedValidateFullName(firstName, lastName);
      }
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Debounced validation functions
  const debouncedValidateEmail = useCallback(
    debounce(async (email: string, field: 'email' | 'additional_email') => {
      if (!email || email.length < 3) return;

      setValidationStates(prev => ({
        ...prev,
        [field]: { ...prev[field], isValidating: true, isDuplicate: false }
      }));

      const result = await ContactValidationService.validateEmail(email, field);
      setValidationStates(prev => ({
        ...prev,
        [field]: result
      }));
    }, 500),
    []
  );

  const debouncedValidateLinkedIn = useCallback(
    debounce(async (linkedin: string) => {
      if (!linkedin || linkedin.length < 10) return;

      setValidationStates(prev => ({
        ...prev,
        linkedin_url: { ...prev.linkedin_url, isValidating: true, isDuplicate: false }
      }));

      const result = await ContactValidationService.validateLinkedIn(linkedin);
      setValidationStates(prev => ({
        ...prev,
        linkedin_url: result
      }));
    }, 500),
    []
  );

  const debouncedValidateFullName = useCallback(
    debounce(async (firstName: string, lastName: string) => {
      if (!firstName || !lastName || (firstName.length + lastName.length < 4)) return;

      if (!selectedCompany?.company_id && !companyId) return;

      setValidationStates(prev => ({
        ...prev,
        full_name: { ...prev.full_name, isValidating: true, isDuplicate: false }
      }));

      const companyIdToUse = companyId ? parseInt(companyId) : selectedCompany?.company_id;
      const result = await ContactValidationService.validateFullName(firstName, lastName, companyIdToUse);
      setValidationStates(prev => ({
        ...prev,
        full_name: result
      }));
    }, 700),
    [selectedCompany, companyId]
  );

  // Debounced company search function
  const debouncedSearchCompanies = useCallback(
    debounce(async (searchTerm: string) => {
      if (searchTerm.length < 2) {
        setCompanySuggestions([]);
        setShowSuggestions(false);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      const suggestions = await ContactSubmissionService.searchCompanies(searchTerm);
      setCompanySuggestions(suggestions);
      setShowSuggestions(suggestions.length > 0);
      setIsSearching(false);
    }, 300),
    []
  );

  // Check for duplicates function
  const checkForDuplicates = async (): Promise<ExistingContact[]> => {
    return ContactValidationService.checkForDuplicates(
      formData,
      companyId,
      selectedCompany?.company_id
    );
  };

  // Save contact function
  const saveContact = async () => {
    try {
      const result = await ContactSubmissionService.saveContact(formData, companyId, selectedCompany, investmentCriteria);
      if (result.success) {
        // Copy selected company investment criteria to the new contact
        if (selectedCompanyIC.length > 0 && result.contact_id) {
          try {
            const copyResponse = await fetch(`/api/contacts/${result.contact_id}/copy-investment-criteria`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ selectedCriteria: selectedCompanyIC }),
            });

            if (copyResponse.ok) {
              const copyResult = await copyResponse.json();
              console.log('Investment criteria copied:', copyResult);
              toast.success(`Contact created and ${copyResult.results.length} investment criteria copied successfully`);
            } else {
              console.error('Failed to copy investment criteria');
              toast.success('Contact created successfully (investment criteria copy failed)');
            }
          } catch (copyError) {
            console.error('Error copying investment criteria:', copyError);
            toast.success('Contact created successfully (investment criteria copy failed)');
          }
        } else {
          toast.success('Contact created successfully');
        }
        
        // Call onSuccess callback if provided
        if (onSuccess && result.contact_id) {
          onSuccess(result.contact_id);
        } else {
          onBack();
        }
      } else {
        throw new Error(result.error || 'Failed to save contact');
      }
    } catch (error) {
      console.error('Error saving contact:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check for validation errors before submitting
    const hasValidationErrors = Object.values(validationStates).some(state => state.isDuplicate);
    if (hasValidationErrors) {
      toast.error('Please resolve validation errors before submitting');
      return;
    }

    setIsSubmitting(true);

    try {
      // Check for duplicates first
      const duplicates = await checkForDuplicates();

      if (duplicates.length > 0) {
        setDuplicateContacts(duplicates);
        setShowDuplicateDialog(true);
        setIsSubmitting(false);
        return;
      }

      await saveContact();
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      toast.error('Failed to add contact');
      setIsSubmitting(false);
    }
  };

  const handleProceedWithDuplicate = async () => {
    setShowDuplicateDialog(false);
    setIsSubmitting(true);
    try {
      await saveContact();
    } catch (error) {
      // Error handling is done in saveContact
    }
  };

  const handleCancelDuplicate = () => {
    setShowDuplicateDialog(false);
    setDuplicateContacts([]);
  };

  // Handle keyboard navigation for company suggestions
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || companySuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < companySuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : companySuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0 && selectedSuggestionIndex < companySuggestions.length) {
          const selectedCompany = companySuggestions[selectedSuggestionIndex];
          setSelectedCompany(selectedCompany);
          setShowSuggestions(false);
          setSelectedSuggestionIndex(-1);
          
          setFormData(prev => ({
            ...prev,
            company_name: selectedCompany.company_name,
            company_website: selectedCompany.company_website || '',
            industry: selectedCompany.industry || '',
            company_address: selectedCompany.company_address || '',
            company_city: selectedCompany.company_city || '',
            company_state: selectedCompany.company_state || '',
            company_country: selectedCompany.company_country || '',
            company_zip: selectedCompany.company_zip || ''
          }));
          
          toast.success('Company selected successfully');
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="w-full p-6 space-y-8">
        {/* Header Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={onBack} 
                className="hover:bg-slate-100 rounded-xl transition-all duration-200"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                  Add New Contact
                </h1>
                <p className="text-slate-600 mt-1">
                  {companyId ? `Create contact for ${selectedCompany?.company_name || 'selected company'}` : 'Create a contact profile with essential information'}
                </p>
              </div>
            </div>
            
            {/* Top Action Buttons */}
            <div className="flex items-center space-x-3">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onBack}
                className="h-11 px-6 rounded-xl border-2 border-slate-200 hover:bg-slate-50 transition-all duration-200 font-medium"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="h-11 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 min-w-[140px] disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Save Contact
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        <form>
          <div className="grid gap-8 transition-all duration-300 grid-cols-1 lg:grid-cols-2">
            {/* Left Column: Contact Information */}
            <div className="space-y-6">
              <ContactFormFields
                formData={formData}
                onChange={handleChange}
                onSelectChange={handleSelectChange}
                validationStates={validationStates}
                filterOptions={filterOptions}
                companyId={companyId}
                selectedCompany={selectedCompany}
                // Pass search props
                showEmailSuggestions={showEmailSuggestions}
                emailSuggestions={emailSuggestions}
                selectedEmailSuggestionIndex={selectedEmailSuggestionIndex}
                isSearchingEmail={isSearchingEmail}
                activeEmailField={activeEmailField}
                onEmailKeyDown={handleEmailKeyDown}
                onEmailContactSelect={handleEmailContactSelect}
                emailSuggestionsRef={emailSuggestionsRef}
              />
            </div>

            {/* Right Column: Company Information */}
            <div className="space-y-6">
              <CompanySearchField
                companyName={formData.company_name}
                onChange={handleChange}
                onKeyDown={handleKeyDown}
                suggestions={companySuggestions}
                showSuggestions={showSuggestions}
                isSearching={isSearching}
                selectedSuggestionIndex={selectedSuggestionIndex}
                onCompanySelect={(company) => {
                  setSelectedCompany(company);
                  setShowSuggestions(false);
                  setSelectedSuggestionIndex(-1);
                  
                  setFormData(prev => ({
                    ...prev,
                    company_name: company.company_name,
                    company_website: company.company_website || '',
                    industry: company.industry || '',
                    company_address: company.company_address || '',
                    company_city: company.company_city || '',
                    company_state: company.company_state || '',
                    company_country: company.company_country || '',
                    company_zip: company.company_zip || ''
                  }));
                  
                  toast.success('Company selected successfully');
                }}
                onInvestmentCriteriaSelect={(selectedCriteria) => {
                  setSelectedCompanyIC(selectedCriteria);
                  console.log('Selected company IC:', selectedCriteria);
                }}
                selectedCompany={selectedCompany}
                companyId={companyId}
              />
            </div>
          </div>

          {/* Investment Criteria Section - Full Width */}
          <div className="mt-8">
            <InvestmentAdditionSection
              onInvestmentCriteriaChange={setInvestmentCriteria}
              selectedCompanyIC={selectedCompanyIC}
            />
          </div>
        </form>

        {/* Duplicate Contact Dialog */}
        <DuplicateContactDialog
          isOpen={showDuplicateDialog}
          onClose={handleCancelDuplicate}
          duplicateContacts={duplicateContacts}
          onAddAnyway={handleProceedWithDuplicate}
        />
      </div>
    </div>
  );
};

export default AddContactSimplified;
